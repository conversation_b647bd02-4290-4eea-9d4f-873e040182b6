# Valic Wire Minigame

Wiring minihra pro FiveM inspirovaná Among Us stylem. Určená pro použití v heistech a jiných scriptech.

## Instalace

1. Zkopíruj složku `valic_wire-minigame` do `resources/[scripts]/[valic]/`
2. <PERSON>ř<PERSON>j do `server.cfg`: `ensure valic_wire-minigame`
3. Restartuj server

## Testování

Pro testování použij dev command:
```
/dev-wire-test
```

## Použití v jiných scriptech

### Export funkce
```lua
exports['valic_wire-minigame']:startWireMinigame(function(success)
    if success then
        print("Hráč úspěšně dokončil wiring!")
        -- Pokračuj s heistem
    else
        print("Hráč selhal nebo zrušil wiring")
        -- Přidej trest nebo restart
    end
end)
```

### Event
```lua
TriggerEvent('valic_wire:start', function(success)
    -- callback logika
end)
```

## Ovládání

- **Drag & Drop**: <PERSON><PERSON><PERSON><PERSON> dráty z levé strany na správné konektory na pravé straně
- **ESC**: Zavře minihru (počítá se jako neúspěch)
- **Auto-close**: Minihra se automaticky zavře po úspěšném dokončení (2 sekundy)

## Funkce

- ✅ Among Us vizuální styl
- ✅ Drag & drop funkcionalita
- ✅ Vizuální feedback (světla se rozsvítí při správném spojení)
- ✅ Zvukový efekt při dokončení
- ✅ Notifikace (úspěch/neúspěch)
- ✅ ESC pro zavření
- ✅ Export pro jiné scripty
- ✅ Dev command pro testování

## Soubory

- `fxmanifest.lua` - Manifest scriptu
- `client.lua` - Client-side logika
- `server.lua` - Server-side logika
- `html/index.html` - UI rozhraní
- `html/style.css` - Styly
- `html/script.js` - JavaScript logika
- `html/task-complete.mp3` - Zvukový efekt

## Autor

Valic - Wire Minigame v1.0.0
