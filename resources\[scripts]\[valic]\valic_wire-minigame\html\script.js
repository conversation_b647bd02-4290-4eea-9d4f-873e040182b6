let completedLights = [0, 0, 0, 0];
let isMinigameActive = false;
let audioTask = null;

// Initialize audio
function initAudio() {
    audioTask = new Audio('task-complete.mp3');
    audioTask.volume = 0.5;
}

// Wire connection targets (right side positions)
const wireTargets = [
    { x: 670, y: 188, wire: 1 },   // Blue wire target
    { x: 670, y: -188, wire: 2 },  // Red wire target  
    { x: 670, y: 0, wire: 3 },     // Yellow wire target
    { x: 670, y: 0, wire: 4 }      // Purple wire target
];

// Initialize draggable elements
function initDraggables() {
    // Blue wire (drag-1)
    Draggable.create('.drag-1', {
        onDrag: function () { 
            updateLine('.line-1', this.x + 120, this.y + 185); 
        },
        onRelease: function () {
            if (this.x !== 670 || this.y !== 188) {
                reset('.drag-1', '.line-1', 70, 185);
                toggleLight(2, false);
            } else if (this.x === 670 && this.y === 188) {
                toggleLight(2, true);
            }
        },
        liveSnap: { points: [{ x: 670, y: 188 }], radius: 20 }
    });

    // Red wire (drag-2)
    Draggable.create('.drag-2', {
        onDrag: function () { 
            updateLine('.line-2', this.x + 120, this.y + 375); 
        },
        onRelease: function () {
            if (this.x !== 670 || this.y !== -188) {
                reset('.drag-2', '.line-2', 60, 375);
                toggleLight(1, false);
            } else if (this.x === 670 && this.y === -188) {
                toggleLight(1, true);
            }
        },
        liveSnap: { points: [{ x: 670, y: -188 }], radius: 20 }
    });

    // Yellow wire (drag-3)
    Draggable.create('.drag-3', {
        onDrag: function () { 
            updateLine('.line-3', this.x + 120, this.y + 560); 
        },
        onRelease: function () {
            if (this.x !== 670 || this.y !== 0) {
                reset('.drag-3', '.line-3', 60, 560);
                toggleLight(3, false);
            } else if (this.x === 670 && this.y === 0) {
                toggleLight(3, true);
            }
        },
        liveSnap: { points: [{ x: 670, y: 0 }], radius: 20 }
    });

    // Purple wire (drag-4)
    Draggable.create('.drag-4', {
        onDrag: function () { 
            updateLine('.line-4', this.x + 120, this.y + 745); 
        },
        onRelease: function () {
            if (this.x !== 670 || this.y !== 0) {
                reset('.drag-4', '.line-4', 60, 745);
                toggleLight(4, false);
            } else if (this.x === 670 && this.y === 0) {
                toggleLight(4, true);
            }
        },
        liveSnap: { points: [{ x: 670, y: 0 }], radius: 20 }
    });
}

// Update wire line position
function updateLine(selector, x, y) {
    gsap.set(selector, {
        attr: {
            x2: x,
            y2: y
        }
    });
}

// Toggle light indicator
function toggleLight(selector, visibility) {
    if (visibility) {
        completedLights[selector - 1] = 1;
        
        // Check if all wires are connected
        if (completedLights.every(light => light === 1)) {
            // All wires connected - success!
            if (audioTask) {
                audioTask.play().catch(e => console.log('Audio play failed:', e));
            }
            
            setTimeout(() => {
                completeMinigame(true);
            }, 2000);
        }
    } else {
        completedLights[selector - 1] = 0;
    }
    
    gsap.to(`.light-${selector}`, {
        opacity: visibility ? 1 : 0,
        duration: 0.3
    });
}

// Reset wire to original position
function reset(drag, line, x, y) {
    gsap.to(drag, {
        duration: 0.3,
        ease: 'power2.out',
        x: 0,
        y: 0
    });
    gsap.to(line, {
        duration: 0.3,
        ease: 'power2.out',
        attr: {
            x2: x,
            y2: y
        }
    });
}

// Get resource name
function getResourceName() {
    return window.location.hostname;
}

// Complete minigame
function completeMinigame(success) {
    if (!isMinigameActive) return;

    isMinigameActive = false;

    // Send result to FiveM
    fetch(`https://${getResourceName()}/minigameComplete`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json; charset=UTF-8',
        },
        body: JSON.stringify({
            success: success
        })
    });

    // Hide UI
    document.getElementById('minigame-container').style.display = 'none';

    // Reset state
    resetMinigame();
}

// Reset minigame state
function resetMinigame() {
    completedLights = [0, 0, 0, 0];
    
    // Reset all wires
    reset('.drag-1', '.line-1', 70, 185);
    reset('.drag-2', '.line-2', 60, 375);
    reset('.drag-3', '.line-3', 60, 560);
    reset('.drag-4', '.line-4', 60, 745);
    
    // Turn off all lights
    toggleLight(1, false);
    toggleLight(2, false);
    toggleLight(3, false);
    toggleLight(4, false);
}

// Handle ESC key
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape' && isMinigameActive) {
        completeMinigame(false);
    }
});

// Listen for messages from FiveM
window.addEventListener('message', function(event) {
    const data = event.data;
    
    if (data.type === 'openMinigame') {
        isMinigameActive = true;
        document.getElementById('minigame-container').style.display = 'flex';
        resetMinigame();
        
        // Initialize everything
        if (!audioTask) {
            initAudio();
        }
        
        // Small delay to ensure DOM is ready
        setTimeout(() => {
            initDraggables();
        }, 100);
    }
});

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    console.log('Wire minigame loaded');
});
