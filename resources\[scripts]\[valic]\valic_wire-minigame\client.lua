local isMinigameActive = false
local currentCallback = nil

-- Inicializace - ujisti se, že NUI je skryté na začátku
CreateThread(function()
    Wait(1000) -- Počkej na načtení
    SetNuiFocus(false, false)
    SendNUIMessage({
        type = "hideUI"
    })
end)

-- Dev command pro testování
RegisterCommand('dev-wire-test', function()
    if isMinigameActive then
        print("^3[Wire Minigame]^7 Minihra už běží!")
        return
    end
    
    print("^2[Wire Minigame]^7 Spouštím testovací wiring minihru...")
    startWireMinigame(function(success)
        if success then
            print("^2[Wire Minigame]^7 Test úspěšný - všechny dráty spojeny!")
        else
            print("^1[Wire Minigame]^7 Test neúspěšný - minihra selhala nebo byla zru<PERSON>ena")
        end
    end)
end, false)

-- Export funkce pro použití v jiných scriptech
function startWireMinigame(callback)
    if isMinigameActive then
        if callback then callback(false) end
        return
    end
    
    isMinigameActive = true
    currentCallback = callback
    
    -- Otevřít NUI
    SetNuiFocus(true, true)
    SendNUIMessage({
        type = "openMinigame"
    })
    
    -- Disable controls během minihry
    CreateThread(function()
        while isMinigameActive do
            DisableAllControlActions(0)
            EnableControlAction(0, 1, true) -- LookLeftRight
            EnableControlAction(0, 2, true) -- LookUpDown
            Wait(0)
        end
    end)
end

-- NUI Callbacks
RegisterNUICallback('minigameComplete', function(data, cb)
    isMinigameActive = false
    SetNuiFocus(false, false)
    
    if currentCallback then
        currentCallback(data.success)
        currentCallback = nil
    end
    
    if data.success then
        -- Úspěšné dokončení
        SetNotificationTextEntry("STRING")
        AddTextComponentString("~g~Wiring úspěšný!~w~\nVšechny dráty správně spojeny")
        DrawNotification(false, false)

        -- Také do chatu pro debug
        TriggerEvent('chat:addMessage', {
            color = {0, 255, 0},
            multiline = true,
            args = {"Wire Minigame", "Všechny dráty úspěšně spojeny!"}
        })
    else
        -- Neúspěšné dokončení
        SetNotificationTextEntry("STRING")
        AddTextComponentString("~r~Wiring selhal!~w~\nSpojení drátů se nezdařilo")
        DrawNotification(false, false)

        -- Také do chatu pro debug
        TriggerEvent('chat:addMessage', {
            color = {255, 0, 0},
            multiline = true,
            args = {"Wire Minigame", "Spojení drátů selhalo!"}
        })
    end
    
    cb('ok')
end)

RegisterNUICallback('closeMinigame', function(data, cb)
    isMinigameActive = false
    SetNuiFocus(false, false)
    
    if currentCallback then
        currentCallback(false)
        currentCallback = nil
    end
    
    SetNotificationTextEntry("STRING")
    AddTextComponentString("~o~Wiring zrušen~w~\nMinihra byla ukončena")
    DrawNotification(false, false)

    -- Také do chatu pro debug
    TriggerEvent('chat:addMessage', {
        color = {255, 165, 0},
        multiline = true,
        args = {"Wire Minigame", "Minihra byla zrušena"}
    })
    
    cb('ok')
end)

-- ESC key handler
RegisterNUICallback('escapePressed', function(data, cb)
    isMinigameActive = false
    SetNuiFocus(false, false)
    
    if currentCallback then
        currentCallback(false)
        currentCallback = nil
    end
    
    cb('ok')
end)

-- Export
exports('startWireMinigame', startWireMinigame)

--[[
    POUŽITÍ EXPORTU:

    -- Z jiného scriptu:
    exports['valic_wire-minigame']:startWireMinigame(function(success)
        if success then
            print("Hráč úspěšně dokončil wiring!")
            -- Zde můžeš pokračovat s heistem
        else
            print("Hráč selhal nebo zrušil wiring")
            -- Zde můžeš přidat trest nebo restart
        end
    end)

    -- Nebo pomocí TriggerEvent:
    TriggerEvent('valic_wire:start', function(success)
        -- callback logika
    end)
--]]

-- Event pro spuštění přes TriggerEvent
RegisterNetEvent('valic_wire:start')
AddEventHandler('valic_wire:start', function(callback)
    startWireMinigame(callback)
end)
