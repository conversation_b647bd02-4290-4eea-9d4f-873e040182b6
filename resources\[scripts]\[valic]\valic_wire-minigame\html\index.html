<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wire Minigame</title>
    <link rel="stylesheet" href="style.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/Draggable.min.js"></script>
</head>
<body>
    <div id="minigame-container" style="display: none;">
        <div class="instructions">Spojte dr<PERSON>ty se správnými konektory</div>
        <div class="esc-hint">ESC - Zavřít</div>
        <svg width="907" height="907" viewBox="0 0 907 907" id="wire-svg">
            <linearGradient id="a" y1="453.5" x2="907" y2="453.5" gradientUnits="userSpaceOnUse">
                <stop offset="0" stop-color="#1d1d1b"/>
                <stop offset="0" stop-color="#272726"/>
                <stop offset=".2" stop-color="#262625" stop-opacity=".93"/>
                <stop offset=".35" stop-color="#232322" stop-opacity=".69"/>
                <stop offset=".48" stop-color="#1e1e1c" stop-opacity=".29"/>
                <stop offset=".51" stop-color="#1d1d1b" stop-opacity=".2"/>
                <stop offset="1" stop-color="#1d1d1b"/>
            </linearGradient>
            <linearGradient id="b" x1="35" y1="-2.43" x2="35" y2="890.57" gradientUnits="userSpaceOnUse">
                <stop offset=".77" stop-color="#393e42"/>
                <stop offset=".83" stop-color="#35393d"/>
                <stop offset=".9" stop-color="#292c2e"/>
                <stop offset=".98" stop-color="#151616"/>
                <stop offset="1" stop-color="#0f0f0f"/>
            </linearGradient>
            <linearGradient id="c" x1="874" y1="-2.43" x2="874" y2="890.57" xlink:href="#b"/>
            
            <g style="isolation:isolate">
                <path fill="#1e2021" d="M0 0h907v907H0z"/>
                <!-- Background paths from original SVG -->
                <path class="c" d="M838 615.45c-.54-.06-1.51-.13-2.87-.24-75.79-6-130.78-23.35-163.56-36.9-41.49-17.15-75-39.34-99.63-65.94-30.57-33-47.53-73-50.43-118.77-5.21-82.25 15.57-282 46.35-389.22L588 5c-30.3 105.55-52.63 306.59-47.52 387.4 4.79 75.74 51.34 132.38 138.36 168.35 31.51 13 84.47 29.77 157.79 35.52 1.59.12 2.73.22 3.36.28z"/>
                
                <!-- Left side connectors -->
                <path class="h" d="M106 381.86a20.48 20.48 0 01-4.77-.49l2-7.75a17.64 17.64 0 0010.15-1.29l3.2 7.34a26.29 26.29 0 01-10.58 2.19z"/>
                <path class="h" d="M106 564.86a20.48 20.48 0 01-4.77-.49l2-7.75a17.64 17.64 0 0010.15-1.29l3.2 7.34a26.29 26.29 0 01-10.58 2.19z"/>
                <path class="h" d="M106 752.86a20.48 20.48 0 01-4.77-.49l2-7.75a17.64 17.64 0 0010.15-1.29l3.2 7.34a26.29 26.29 0 01-10.58 2.19z"/>
                <path class="h" d="M106 191.86a20.48 20.48 0 01-4.77-.49l2-7.75a17.64 17.64 0 0010.15-1.29l3.2 7.34a26.29 26.29 0 01-10.58 2.19z"/>
                
                <!-- Right side connectors -->
                <path class="h" d="M763.69 751.86a20.48 20.48 0 004.77-.49l-2-7.75a17.64 17.64 0 01-10.15-1.29l-3.2 7.34a26.33 26.33 0 0010.58 2.19z"/>
                <path class="h" d="M763.69 566.86a20.48 20.48 0 004.77-.49l-2-7.75a17.64 17.64 0 01-10.15-1.29l-3.2 7.34a26.33 26.33 0 0010.58 2.19z"/>
                <path class="h" d="M763.69 382.86a20.48 20.48 0 004.77-.49l-2-7.75a17.64 17.64 0 01-10.15-1.29l-3.2 7.34a26.33 26.33 0 0010.58 2.19z"/>
                <path class="h" d="M763.69 193.86a20.48 20.48 0 004.77-.49l-2-7.75a17.64 17.64 0 01-10.15-1.29l-3.2 7.34a26.33 26.33 0 0010.58 2.19z"/>
                
                <!-- Left side colored panels -->
                <path class="k" d="M7.5 140.5h56v63h-56z"/>
                <path class="k" d="M7.5 330.5h56v63h-56z"/>
                <path class="k" d="M7.5 515.5h56v63h-56z"/>
                <path class="k" d="M7.5 701.5h56v63h-56z"/>
                
                <!-- Colored wire sections -->
                <path class="l" d="M8.51 143.16H59.5v24.34H8.51z"/>
                <path class="m" d="M8.5 167.5h66v33h-66z"/>
                <path class="n" d="M8.5 176.5h66v17h-66z"/>
                
                <path class="l" d="M8.51 333.16H59.5v24.34H8.51z"/>
                <path class="o" d="M8.5 357.5h66v33h-66z"/>
                <path class="p" d="M8.5 366.5h66v17h-66z"/>
                
                <path class="l" d="M8.51 518.16H59.5v24.34H8.51z"/>
                <path class="q" d="M8.5 542.5h66v33h-66z"/>
                <path class="r" d="M8.5 551.5h66v17h-66z"/>
                
                <path class="l" d="M8.51 704.16H59.5v24.34H8.51z"/>
                <path class="s" d="M8.5 728.5h66v33h-66z"/>
                <path class="t" d="M8.5 737.5h66v17h-66z"/>
                
                <!-- Right side panels -->
                <path class="k" d="M845.5 142.5h56v63h-56z"/>
                <path class="k" d="M845.5 330.5h56v63h-56z"/>
                <path class="k" d="M845.5 514.5h56v63h-56z"/>
                <path class="k" d="M845.5 699.5h56v63h-56z"/>
                
                <!-- Right side colored sections -->
                <path class="l light light-1" d="M850.51 145.16h50.99v24.34h-50.99z"/>
                <path class="l light light-2" d="M850.51 333.16h50.99v24.34h-50.99z"/>
                <path class="l light light-3" d="M850.51 517.16h50.99v24.34h-50.99z"/>
                <path class="l light light-4" d="M850.51 702.16h50.99v24.34h-50.99z"/>
                
                <path class="o" d="M794.5 169.5h108v33h-108z"/>
                <path class="p" d="M794.5 178.5h108v17h-108z"/>
                <path class="m" d="M791.5 357.5h111v33h-111z"/>
                <path class="n" d="M791.5 366.5h111v17h-111z"/>
                <path class="q" d="M791.5 541.5h110v33h-110z"/>
                <path class="r" d="M791.5 550.5h110v17h-110z"/>
                <path class="s" d="M793.5 726.5h108v33h-108z"/>
                <path class="t" d="M793.5 735.5h108v17h-108z"/>
                
                <!-- Border -->
                <path style="mix-blend-mode:darken" fill="url(#a)" d="M0 0h907v907H0z"/>
                <path fill="none" stroke="black" stroke-width="14" d="M0 0h907v907H0z"/>
                
                <!-- Draggable areas -->
                <rect x="60" y="165" width="60" height="40" class="drag drag-1" />
                <rect x="60" y="355" width="60" height="40" class="drag drag-2" />
                <rect x="60" y="540" width="60" height="40" class="drag drag-3" />
                <rect x="60" y="725" width="60" height="40" class="drag drag-4" />
                
                <!-- Wire lines -->
                <line x1="70" y1="185" x2="70" y2="185" class="line line-back line-1" />
                <line x1="70" y1="185" x2="70" y2="185" class="line line-1" />
                <line x1="65" y1="375" x2="65" y2="375" class="line line-back line-2" />
                <line x1="65" y1="375" x2="65" y2="375" class="line line-2" />
                <line x1="65" y1="560" x2="65" y2="560" class="line line-back line-3" />
                <line x1="65" y1="560" x2="65" y2="560" class="line line-3" />
                <line x1="65" y1="745" x2="65" y2="745" class="line line-back line-4" />
                <line x1="65" y1="745" x2="65" y2="745" class="line line-4" />
            </g>
        </svg>
    </div>
    
    <script src="script.js"></script>
</body>
</html>
