<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wire Minigame</title>
    <link rel="stylesheet" href="style.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/Draggable.min.js"></script>
</head>
<body>
    <div id="minigame-container" style="display: none;">
        <div class="instructions">Spojte dr<PERSON>ty se správnými konektory</div>
        <div class="esc-hint">ESC - Zavřít</div>
        <svg width="907" height="907" viewBox="0 0 907 907" id="wire-svg">
            <linearGradient id="a" y1="453.5" x2="907" y2="453.5" gradientUnits="userSpaceOnUse">
                <stop offset="0" stop-color="#1d1d1b"/>
                <stop offset="0" stop-color="#272726"/>
                <stop offset=".2" stop-color="#262625" stop-opacity=".93"/>
                <stop offset=".35" stop-color="#232322" stop-opacity=".69"/>
                <stop offset=".48" stop-color="#1e1e1c" stop-opacity=".29"/>
                <stop offset=".51" stop-color="#1d1d1b" stop-opacity=".2"/>
                <stop offset="1" stop-color="#1d1d1b"/>
            </linearGradient>
            <linearGradient id="b" x1="35" y1="-2.43" x2="35" y2="890.57" gradientUnits="userSpaceOnUse">
                <stop offset=".77" stop-color="#393e42"/>
                <stop offset=".83" stop-color="#35393d"/>
                <stop offset=".9" stop-color="#292c2e"/>
                <stop offset=".98" stop-color="#151616"/>
                <stop offset="1" stop-color="#0f0f0f"/>
            </linearGradient>
            <linearGradient id="c" x1="874" y1="-2.43" x2="874" y2="890.57" xlink:href="#b"/>
            
            <g style="isolation:isolate">
                <path fill="#c8d6e5" d="M0 0h907v907H0z"/>
                <!-- Background paths from original SVG -->
                <path class="c" d="M838 615.45c-.54-.06-1.51-.13-2.87-.24-75.79-6-130.78-23.35-163.56-36.9-41.49-17.15-75-39.34-99.63-65.94-30.57-33-47.53-73-50.43-118.77-5.21-82.25 15.57-282 46.35-389.22L588 5c-30.3 105.55-52.63 306.59-47.52 387.4 4.79 75.74 51.34 132.38 138.36 168.35 31.51 13 84.47 29.77 157.79 35.52 1.59.12 2.73.22 3.36.28z"/>
                <path class="d" d="M723.54 409.4q-9.16 0-18.14-.41c-54.53-2.51-106.1-16.15-162.27-42.91-45.22-21.55-77.07-89.15-94.69-200.95C435.59 83.58 435.28 7 435.28 6.2h19c0 .75.32 76.1 13 156.23C483.6 266 512.66 330.52 551.3 348.92c110.05 52.44 210.92 54.93 348.16 8.58l6.08 18c-67.37 22.75-127.14 33.9-182 33.9z"/>
                <path class="e" d="M802.12 286.19c-48.89 0-116.11-4.85-174.4-26.27-28.81-10.59-52.24-31.05-69.63-60.8-13.58-23.21-23.48-52.12-29.45-85.9-10-56.88-5.56-107.58-5.36-109.71l18.92 1.74c-.06.68-4.44 50.72 5.22 105 12.6 70.9 41.82 115.24 86.86 131.79 113.27 41.67 265.24 18.83 266.72 18.6l3 18.77c-1.61.25-39.91 6.11-93.22 6.73-2.84.03-5.72.05-8.66.05zM542.2 5.25l-9.46-.87 9.46.87z"/>
                <path class="d" d="M564.31 908.05c-.12-2.15-12.69-218-22.63-436.16C521.47 28.25 531.45 7.87 535.21.2l17.06 8.36c-1.78 4.08-5.66 27.56-2.84 156.19 1.68 76.69 5.48 180.3 11.31 308 9.91 217.4 22.42 432.05 22.54 434.25z"/>
                <path class="f" d="M437.33 908l-19-.23c0-2.19 2.74-221.32 8.25-440.78C437.82 19.6 449.22 6.94 454.7.85l14.12 12.71a4.64 4.64 0 00.61-.82c-2.06 4-7.52 27.05-13.71 155.12-3.69 76.35-7.16 179.62-10.33 306.93-5.39 216.84-8.03 431.07-8.06 433.21zM809.3 715.45c-18.52 0-38.52-.48-58.78-1.76-106.86-6.78-169.57-32.1-186.37-75.24C526.86 542.7 546.94 27.68 547.82 5.8l19 .76c-.21 5.31-20.81 532.91 15.05 625 13.68 35.12 72.35 57 169.67 63.16 75.88 4.84 148.36-1.94 149.08-2l1.8 18.92c-.56.03-40.92 3.81-93.12 3.81z"/>
                <path class="e" d="M60.47 726.62c-31.85 0-51.9-.78-52.35-.8l.76-19c1 0 103.72 4 215.44-6.35 145.77-13.52 240.53-44.87 274-90.65 33.94-46.39 51-164.9 49.35-342.73-1.23-135.54-13.13-258.44-13.25-259.66l18.91-1.86c.12 1.24 12.09 124.85 13.37 261.23 1.73 184.63-16.11 303.82-53 354.26-37.35 51-134.18 84.14-287.8 98.37-63.64 5.89-124.35 7.19-165.43 7.19z"/>
                <path class="c" d="M132.2 545.37a1197 1197 0 01-125.82-6.61l2.24-18.86c2.75.32 275.82 31.71 364.55-47.49 36.15-32.26 60.91-122.55 71.6-261.1 8.19-106.14 5-203.52 5-204.49l19-.64c0 1 3.23 99.26-5 206.42-11.22 145.7-37.43 237.88-77.89 274-40.3 36-116.66 55.62-227 58.45-9.05.22-17.98.32-26.68.32z"/>
                <path class="e" d="M286.57 299.68a345.5 345.5 0 01-37.6-2.24C146.67 286.3 8.79 188.39 3 184.23l11-15.46c1.38 1 139.66 99.19 237 109.79 63 6.86 103.74-6.26 140.25-32C463 196 449.1 125 455 6l19 1c-3.27 65.51 3 118-11.6 163.48-12.69 39.51-34 69.45-58.8 90.57-36.42 31.04-79.72 38.63-117.03 38.63z"/>
                <path class="d" d="M251.21 228.7c-31.08 0-62.8-5.84-94.43-17.41-27.89-10.2-55.77-24.86-82.86-43.55C27.89 136 .3 103.85-.85 102.5l14.46-12.33c.27.32 27.42 31.87 71.53 62.22 40.25 27.7 103.36 60.09 175.47 57.12 78-3.23 121.3-15.37 149.4-41.89 30.54-28.82 45.72-78.74 49.25-161.85l19 .81c-1.87 44.19-7 79.2-15.55 107-8.91 28.88-21.87 51.07-39.64 67.84-31.77 30-78.6 43.62-161.66 47.05-3.41.16-6.8.23-10.2.23z"/>
                <path class="f" d="M226.25 455.3q-42.93 0-100.76-12a967.1 967.1 0 01-122.4-34.5L9.67 391a948.51 948.51 0 00119.89 33.76c82.92 17.18 142.67 15.09 172.82-6 7.09-5 14.24-9.69 21.15-14.26 35.29-23.31 68.62-45.33 94.2-99.51 29.13-61.71 32.7-160.09 35.61-300.77l19 .4c-3 145.39-6.71 243.41-37.43 308.48-27.85 59-65 83.53-100.91 107.25-6.8 4.5-13.84 9.15-20.72 14-19.98 13.94-49.1 20.95-87.03 20.95z"/>
                
                <!-- Left side connectors -->
                <path class="h" d="M106 381.86a20.48 20.48 0 01-4.77-.49l2-7.75a17.64 17.64 0 0010.15-1.29l3.2 7.34a26.29 26.29 0 01-10.58 2.19z"/>
                <path class="h" d="M106 564.86a20.48 20.48 0 01-4.77-.49l2-7.75a17.64 17.64 0 0010.15-1.29l3.2 7.34a26.29 26.29 0 01-10.58 2.19z"/>
                <path class="h" d="M106 752.86a20.48 20.48 0 01-4.77-.49l2-7.75a17.64 17.64 0 0010.15-1.29l3.2 7.34a26.29 26.29 0 01-10.58 2.19z"/>
                <path class="h" d="M106 191.86a20.48 20.48 0 01-4.77-.49l2-7.75a17.64 17.64 0 0010.15-1.29l3.2 7.34a26.29 26.29 0 01-10.58 2.19z"/>
                
                <!-- Right side connectors -->
                <path class="h" d="M763.69 751.86a20.48 20.48 0 004.77-.49l-2-7.75a17.64 17.64 0 01-10.15-1.29l-3.2 7.34a26.33 26.33 0 0010.58 2.19z"/>
                <path class="h" d="M763.69 566.86a20.48 20.48 0 004.77-.49l-2-7.75a17.64 17.64 0 01-10.15-1.29l-3.2 7.34a26.33 26.33 0 0010.58 2.19z"/>
                <path class="h" d="M763.69 382.86a20.48 20.48 0 004.77-.49l-2-7.75a17.64 17.64 0 01-10.15-1.29l-3.2 7.34a26.33 26.33 0 0010.58 2.19z"/>
                <path class="h" d="M763.69 193.86a20.48 20.48 0 004.77-.49l-2-7.75a17.64 17.64 0 01-10.15-1.29l-3.2 7.34a26.33 26.33 0 0010.58 2.19z"/>
                
                <!-- Left side panels -->
                <path class="k" d="M7.5 140.5h56v63h-56z"/>
                <path class="k" d="M7.5 330.5h56v63h-56z"/>
                <path class="k" d="M7.5 515.5h56v63h-56z"/>
                <path class="k" d="M7.5 701.5h56v63h-56z"/>
                
                <!-- Colored wire sections -->
                <path class="l" d="M8.51 143.16H59.5v24.34H8.51z"/>
                <path class="m" d="M8.5 167.5h66v33h-66z"/>
                <path class="n" d="M8.5 176.5h66v17h-66z"/>
                
                <path class="l" d="M8.51 333.16H59.5v24.34H8.51z"/>
                <path class="o" d="M8.5 357.5h66v33h-66z"/>
                <path class="p" d="M8.5 366.5h66v17h-66z"/>
                
                <path class="l" d="M8.51 518.16H59.5v24.34H8.51z"/>
                <path class="q" d="M8.5 542.5h66v33h-66z"/>
                <path class="r" d="M8.5 551.5h66v17h-66z"/>
                
                <path class="l" d="M8.51 704.16H59.5v24.34H8.51z"/>
                <path class="s" d="M8.5 728.5h66v33h-66z"/>
                <path class="t" d="M8.5 737.5h66v17h-66z"/>
                
                <!-- Right side panels -->
                <path class="k" d="M845.5 142.5h56v63h-56z"/>
                <path class="k" d="M845.5 330.5h56v63h-56z"/>
                <path class="k" d="M845.5 514.5h56v63h-56z"/>
                <path class="k" d="M845.5 699.5h56v63h-56z"/>
                
                <!-- Right side colored sections -->
                <path class="l light light-1" d="M850.51 145.16h50.99v24.34h-50.99z"/>
                <path class="l light light-2" d="M850.51 333.16h50.99v24.34h-50.99z"/>
                <path class="l light light-3" d="M850.51 517.16h50.99v24.34h-50.99z"/>
                <path class="l light light-4" d="M850.51 702.16h50.99v24.34h-50.99z"/>
                
                <path class="o" d="M794.5 169.5h108v33h-108z"/>
                <path class="p" d="M794.5 178.5h108v17h-108z"/>
                <path class="m" d="M791.5 357.5h111v33h-111z"/>
                <path class="n" d="M791.5 366.5h111v17h-111z"/>
                <path class="q" d="M791.5 541.5h110v33h-110z"/>
                <path class="r" d="M791.5 550.5h110v17h-110z"/>
                <path class="s" d="M793.5 726.5h108v33h-108z"/>
                <path class="t" d="M793.5 735.5h108v17h-108z"/>
                
                <!-- Border -->
                <path style="mix-blend-mode:darken" fill="url(#a)" d="M0 0h907v907H0z"/>
                <path fill="none" stroke="black" stroke-width="14" d="M0 0h907v907H0z"/>
                
                <!-- Draggable areas -->
                <rect x="60" y="165" width="60" height="40" class="drag drag-1" />
                <rect x="60" y="355" width="60" height="40" class="drag drag-2" />
                <rect x="60" y="540" width="60" height="40" class="drag drag-3" />
                <rect x="60" y="725" width="60" height="40" class="drag drag-4" />
                
                <!-- Wire lines -->
                <line x1="70" y1="185" x2="70" y2="185" class="line line-back line-1" />
                <line x1="70" y1="185" x2="70" y2="185" class="line line-1" />
                <line x1="65" y1="375" x2="65" y2="375" class="line line-back line-2" />
                <line x1="65" y1="375" x2="65" y2="375" class="line line-2" />
                <line x1="65" y1="560" x2="65" y2="560" class="line line-back line-3" />
                <line x1="65" y1="560" x2="65" y2="560" class="line line-3" />
                <line x1="65" y1="745" x2="65" y2="745" class="line line-back line-4" />
                <line x1="65" y1="745" x2="65" y2="745" class="line line-4" />

                <!-- Border -->
                <path fill="none" stroke="black" stroke-width="14" d="M0 0h907v907H0z"/>
            </g>
        </svg>
    </div>
    
    <script src="script.js"></script>
</body>
</html>
