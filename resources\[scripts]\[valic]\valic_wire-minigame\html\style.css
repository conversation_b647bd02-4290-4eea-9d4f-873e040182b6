body {
    margin: 0;
    padding: 0;
    font-family: Arial, sans-serif;
    background: transparent;
}

#minigame-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

svg {
    width: 90vmin;
    height: auto;
    cursor: default;
}

.light {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.drag {
    fill: white; 
    opacity: 0;
    cursor: grab;
}

.drag:active {
    cursor: grabbing;
}

.line {
    stroke-width: 18px;
    pointer-events: none;
}

.line-back {
    stroke-width: 30px;
    pointer-events: none;
}

.line-1 {
    stroke: #324d9c;
}

.line-1.line-back {
    stroke: #25378d;
}

.line-2 {
    stroke: #e52320;
}

.line-2.line-back {
    stroke: #a71916;
}

.line-3 {
    stroke: #ffeb13;
}

.line-3.line-back {
    stroke: #aa9f17;
}

.line-4 {
    stroke: #a6529a;
}

.line-4.line-back {
    stroke: #90378c;
}

/* SVG Styles */
.c{fill:#273065;stroke:#1a1b36;stroke-miterlimit:10;stroke-width:5px}
.d{fill:#71160e;stroke:#280f10;stroke-miterlimit:10;stroke-width:5px}
.e{fill:#8c6c15;stroke:#38321a;stroke-miterlimit:10;stroke-width:5px}
.f{fill:#212021;stroke:#000;stroke-miterlimit:10;stroke-width:5px}
.h{fill:#9b3015;stroke:#471d12;stroke-linecap:round;stroke-linejoin:round;stroke-width:5px}
.k{fill:none;stroke:#1d1d1b;stroke-miterlimit:10;stroke-width:6px}
.l{fill:#d9c905}
.m{fill:#25378d}
.n{fill:#324d9c}
.o{fill:#a71916}
.p{fill:#e52320}
.q{fill:#aa9f17}
.r{fill:#ffeb13}
.s{fill:#90378c}
.t{fill:#a6529a}
.u{fill:#1d1d1b;stroke:#38321a;stroke-miterlimit:10;stroke-width:5px}
.v{fill:#5b5c64}
.w{fill:#292829}
.x{fill:#2f3038}
.y{fill:none;stroke:#252526;stroke-linecap:round;stroke-linejoin:round;stroke-width:5px}

/* Instructions */
.instructions {
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    color: white;
    text-align: center;
    font-size: 18px;
    background: rgba(0, 0, 0, 0.7);
    padding: 10px 20px;
    border-radius: 5px;
    z-index: 10000;
}

/* ESC hint */
.esc-hint {
    position: absolute;
    bottom: 20px;
    right: 20px;
    color: #ccc;
    font-size: 14px;
    background: rgba(0, 0, 0, 0.7);
    padding: 5px 10px;
    border-radius: 3px;
}
